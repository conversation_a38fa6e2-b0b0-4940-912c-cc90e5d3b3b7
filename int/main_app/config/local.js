var path = require('path');
var fs = require('fs');

module.exports = {
    port: process.env.PORT || 8001, //
    connections: {
        knexConnection: {
            host: 'vwfs-int.database.windows.net',
            port: 1433,
            user: fs.readFileSync('/mnt/secrets-store/sqluserusr', 'utf8').trim(),
            password: fs.readFileSync('/mnt/secrets-store/sqluserpw', 'utf8').trim(),
            database: 'vwfs-int',
            requestTimeout: 120000,
            pool: {
                idleTimeoutMillis: 120000,
            },
            options: {
                encrypt: true,
                connectTimeout: 30000
            }
        }
    },
    hostname: 'https://vwfs.int.teamassistant.app/api',
    ad: {
        binaryObjectGUID: true
    },
    db: {
        debug: false,
        client: 'mssql', // you can choose between 'oracledb' and 'mssql'
    },
    mail: {
        sendingEnabled: false,
    },
    frontendUrl: 'https://vwfs.int.teamassistant.app', // frontend url to enable XHR and disable browsers CORS, in same domain environment is the same as backend url
    langs: ['cs', 'en', 'sk', 'ro', 'pl'], // the first is main language
    dms: {
        tikaUrl: 'http://tika:9998', // url to tika, turnoff with tikaEnabled
        elasticVersion: 7, // used version of elasticsearch (5 or 7)
        elasticUrl: 'http://elastic:9200', // url to elasticSearch, turnoff with fulltext
        fulltext: 'tas',
        storagePath: '/app/tas/dms', // path where DMS files will be stored
        security: {
            allowedExternalSources: [
                '/app/tas/dms',
            ],
            encryptContent: false,
            encryptFileNames: false,
            elasticsearch: {
                secured: true,
                user: 'elastic',
                password: fs.readFileSync('/mnt/secrets-store/elasticpass', 'utf8').trim(),
            },
        },
    },
    crons: {
        runOnStart: true,
    },
    logger: {
        enableStdoutLog: false,
        elastic: {
            enabled: true,
            pinoConfig: {
                index: "tas-vwfs-int-logs",
                consistency: "one",
                node: "http://elastic:9200",
                "es-version": 7,
                op_type: "create",
                auth: {
                    username: "elastic",
                    password: fs.readFileSync('/mnt/secrets-store/elasticpass', 'utf-8').trim(),
                },
            },
        },
        arango: {
            enabled: false,
        },
    },
    redis: {
        // host: 'redis',
        // port: 6379,
        host: 'vwfs-int.redis.cache.windows.net',
        port: 6380,
        password: '',
        tls: {},
    },

    customAssetsPath: '/app/tas/storage/assets',
    csvStorageDir: '/app/tas/storage/assets/csv',

    plugins: {
       destination: '/app/tas/storage/plugins',
    },

    paths: {
      tmp: '/app/tas/storage/tmp',
      userPhotoPath: '/app/tas/storage/photos',
    },

    tas: {
      cachePath: '/app/tas/storage/cache',
    },

    security: {
        saltRounds: 4,
    },

};
