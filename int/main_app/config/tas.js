var config = require('./tas.base.js');

config.backendUrl = 'https://vwfs.int.teamassistant.app/api';
config.multitenantBackend = false;
config.port = 8000; // listening on port
config.menuLink =  [
    { url: '/assets/uploads/pdf', title: 'Manuals', newWindow: false, iframe: true }
];
config.cloneDynRows = true;

config.socketNotifs = false;

config.responseHeaders = {
    'X-Frame-Options': 'sameorigin', // clickjacking prevention
    'X-Content-Type-Options': 'nosniff',
    'X-Permitted-Cross-Domain-Policies': "none",
    'Referrer-Policy': 'origin',
    'Cross-Origin-Resource-Policy': 'cross-origin',
    'Cross-Origin-Opener-Policy': 'same-origin',
    //'Cross-Origin-Embedder-Policy': 'require-corp',
    'Content-Security-Policy': `default-src 'self' 'unsafe-inline' 'unsafe-eval' data: vwfs-back.int.teamassistant.app`,
};

config.assetsStoragePath = '/app/tas/storage/assets';

module.exports = config;
