apiVersion: apps/v1
kind: Deployment
metadata:
  name: tas-front
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tas-front
  template:
    metadata:
      labels:
        app: tas-front
    spec:
      imagePullSecrets:
        - name: registry-auth
      containers:
        - name: tas-front
          image: registry.neit.cz/tas/frontend:5.7.11-7c1b9b0e86-2503171604
          imagePullPolicy: Always
          env:
            - name: LANG
              value: "C.UTF-8"
          envFrom:
            - configMapRef:
                name: tas-env-config
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          ports:
            - containerPort: 8000
          volumeMounts:
            - name: config
              mountPath: "/app/tas/frontend/config/tas.js"
              readOnly: false
              subPath: "tas.js"
            - name: frontend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
      initContainers:
        - name: folders
          image: elasticsearch:7.17.16
          imagePullPolicy: Always
          command: ["/bin/bash", "-c"]
          args: ["/bin/prepare-tas-frontend-folders.sh"]
          resources:
            requests:
              memory: "1024Mi"
              cpu: "200m"
            limits:
              memory: "6144Mi"
              cpu: "1500m"
          volumeMounts:
            - name: frontend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: prepare-tas-frontend-folders
              mountPath: /bin/prepare-tas-frontend-folders.sh
              readOnly: true
              subPath: prepare-tas-frontend-folders.sh
      volumes:
        - name: config
          configMap:
            name: tas-front-config
        - name: frontend-storage
          azureFile:
            secretName: kvsecret
            shareName: frontend-storage
            readOnly: false
        - name: prepare-tas-frontend-folders
          configMap:
            defaultMode: 0755
            name: prepare-tas-frontend-folders-configmap
---
apiVersion: v1
kind: Service
metadata:
  name: tas-front
spec:
  selector:
    app: tas-front
  ports:
    - port: 80
      targetPort: 8000
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prepare-tas-frontend-folders-configmap
data:
  prepare-tas-frontend-folders.sh: |
    #!/bin/bash
    #exit 0

    set -o errexit

    readonly NODE_UID=1000
    readonly NODE_GID=1000

    mkdir -p /app/tas/storage/assets && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/assets
