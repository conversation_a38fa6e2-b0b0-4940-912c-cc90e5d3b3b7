apiVersion: apps/v1
kind: Deployment
metadata:
  name: tas-backend-cron
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tas-backend-cron
  template:
    metadata:
      labels:
        app: tas-backend-cron
      annotations:
        app.kubernetes.io/part-of: "tas"
        app.kubernetes.io/name: "tas-backend-cron"
        app.kubernetes.io/version: "5.7.11-7c1b9b0e86-**********"
    spec:
      imagePullSecrets:
        - name: registry-auth
      containers:
        - name: tas-backend-cron
          image: registry.neit.cz/tas/backend:5.7.11-7c1b9b0e86-**********
          imagePullPolicy: Always
          command: ["npm", "run", "start:cron"]
          env:
            - name: TAS_IMAGE_TAG
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['app.kubernetes.io/version']
            - name: NODE_OPTIONS
              value: "--openssl-legacy-provider"
            - name: LANG
              value: "C.UTF-8"
          envFrom:
            - configMapRef:
                name: tas-env-config
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "4Gi"
              cpu: "1500m"
          ports:
            - containerPort: 8001
          volumeMounts:
            - name: tas-db-secret
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: config
              mountPath: "/app/tas/backend/config/config/local.js"
              readOnly: false
              subPath: "local.js"
            - name: dms-storage
              mountPath: "/app/tas/dms"
              readOnly: false
            - name: backend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: frontend-storage
              mountPath: "/data/tas-front-uploads"
              readOnly: false
      initContainers:
        - name: folders
          image: elasticsearch:7.17.16
          imagePullPolicy: Always
          command: ["/bin/bash", "-c"]
          args: ["/bin/prepare-tas-backend-folders.sh"]
          resources:
            requests:
              memory: "1024Mi"
              cpu: "200m"
            limits:
              memory: "6144Mi"
              cpu: "1500m"
          volumeMounts:
            - name: tas-db-secret
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: config
              mountPath: "/app/tas/backend/config/config/local.js"
              readOnly: false
              subPath: "local.js"
            - name: dms-storage
              mountPath: "/app/tas/dms"
              readOnly: false
            - name: backend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: prepare-tas-backend-folders
              mountPath: /bin/prepare-tas-backend-folders.sh
              readOnly: true
              subPath: prepare-tas-backend-folders.sh
        - name: migration
          image: registry.neit.cz/tas/backend:5.7.11-7c1b9b0e86-**********
          imagePullPolicy: Always
          command: ["/bin/bash", "-c"]
          args: ["/bin/tas-backend-migration.sh"]
          env:
            - name: TAS_IMAGE_TAG
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['app.kubernetes.io/version']
          envFrom:
            - configMapRef:
                name: tas-env-config
          resources:
            requests:
              memory: "1024Mi"
              cpu: "200m"
            limits:
              memory: "6144Mi"
              cpu: "1500m"
          volumeMounts:
            - name: tas-db-secret
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: config
              mountPath: "/app/tas/backend/config/config/local.js"
              readOnly: false
              subPath: "local.js"
            - name: dms-storage
              mountPath: "/app/tas/dms"
              readOnly: false
            - name: backend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: tas-backend-init-migration
              mountPath: /bin/tas-backend-migration.sh
              readOnly: true
              subPath: tas-backend-migration.sh
      volumes:
        - name: tas-db-secret
          secret:
            secretName: tas-db-secret
        - name: config
          configMap:
            name: tas-backend-config
        - name: dms-storage
          azureFile:
            secretName: kvsecret
            shareName: dms-storage
            readOnly: false
        - name: backend-storage
          azureFile:
            secretName: kvsecret
            shareName: backend-storage
            readOnly: false
        - name: frontend-storage
          azureFile:
            secretName: kvsecret
            shareName: frontend-storage
            readOnly: false
        - name: tas-backend-init-migration
          configMap:
            defaultMode: 0755
            name: tas-backend-init-migration-configmap
        - name: prepare-tas-backend-folders
          configMap:
            defaultMode: 0755
            name: prepare-tas-backend-folders-configmap
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tas-backend-init-migration-configmap
data:
  tas-backend-migration.sh: |
    #!/bin/bash
    #sleep 3600
    #exit 0

    status_storage="/app/tas/storage/cache"

    echo "tag=${TAS_IMAGE_TAG}"

    if [ "${TAS_IMAGE_TAG}" == "" ]; then
        echo "Can not identify TAS image version. Migration skipped."
        exit 0
    fi

    migration_status_file="${status_storage}/init-migration-${TAS_IMAGE_TAG}"
    migration_log_file="${status_storage}/migration-${TAS_IMAGE_TAG}.log"

    if [ -e "${migration_status_file}" ]; then
        echo "Migration already finished for this version of image."
        exit 0
    fi

    date >> "${migration_log_file}"
    npm run mig >> "${migration_log_file}" 2>&1
    migration_result=$?

    if [ "${migration_result}" == "0" ]; then
        echo "Migration succeeded, creating ${migration_status_file}"
        touch "${migration_status_file}"
    fi

    exit ${migration_result}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prepare-tas-backend-folders-configmap
data:
  prepare-tas-backend-folders.sh: |
    #!/bin/bash
    #exit 0

    set -o errexit

    readonly NODE_UID=1000
    readonly NODE_GID=1000

    chown ${NODE_UID}:${NODE_GID} /app/tas/dms
    chown ${NODE_UID}:${NODE_GID} /app/tas/storage

    mkdir -p /app/tas/storage/cache && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/cache
    mkdir -p /app/tas/storage/tmp && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/tmp
    mkdir -p /app/tas/storage/plugins && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/plugins
    mkdir -p /app/tas/storage/assets && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/assets
    mkdir -p /app/tas/storage/photos && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/photos
    mkdir -p /app/tas/storage/assets/csv && chown ${NODE_UID}:${NODE_GID} /app/tas/storage/assets/csv
