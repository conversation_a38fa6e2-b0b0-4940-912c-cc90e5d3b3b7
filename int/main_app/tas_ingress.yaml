apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "200m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
  name: tas-ingress-front
  labels:
    name: tas-ingress-front
spec:
  tls:
  - hosts:
    - vwfs.int.teamassistant.app
    secretName: tas-vwfs-int-ingress-tls
  ingressClassName: nginx
  rules:
  - host: vwfs.int.teamassistant.app
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: tas-front
            port:
              number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "200m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
  name: tas-ingress-api
  labels:
    name: tas-ingress-api
spec:
  tls:
  - hosts:
    - vwfs.int.teamassistant.app
    secretName: tas-vwfs-int-ingress-tls
  ingressClassName: nginx
  rules:
  - host: vwfs.int.teamassistant.app
    http:
      paths:
      - pathType: ImplementationSpecific
        path: "/api(/|$)(.*)"
        backend:
          service:
            name: tas-backend
            port:
              number: 80
---
