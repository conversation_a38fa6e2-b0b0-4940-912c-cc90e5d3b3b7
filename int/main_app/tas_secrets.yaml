# Database secrets
apiVersion: v1
kind: Secret
metadata:
  name: tas-db-secret
type: Opaque
data:
  sqluserusr: placeholder_replace-this
  sqluserpw: placeholder_replace-this
---
# Elasticsearch secret
apiVersion: v1
kind: Secret
metadata:
  name: tas-elastic-secret
type: Opaque
data:
  elasticpass: placeholder_replace-this
---
# TLS certificate secret
apiVersion: v1
kind: Secret
metadata:
  name: tas-int-ingress-tls
type: kubernetes.io/tls
data:
  tls.crt: placeholder_replace-this
  tls.key: placeholder_replace-this
---
# Docker registry secret
apiVersion: v1
kind: Secret
metadata:
  name: registry-auth
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: placeholder_replace-this
