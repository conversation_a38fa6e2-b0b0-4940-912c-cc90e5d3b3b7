apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: tas-secret-loader
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: 00000000-0000-0000-0000-************
    tenantId: 00000000-0000-0000-0000-************
    keyvaultName: "nc-kv-pc-vwfs-int"
    objects:  |
      array:
        - |
          objectName: sqluserusr
          objectType: secret
        - |
          objectName: sqluserpw
          objectType: secret
        - |
          objectName: elasticpass
          objectType: secret
        - |
          objectName: registry-auth
          objectType: secret
---
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: k8s-secret-loader
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: 00000000-0000-0000-0000-************
    tenantId: 00000000-0000-0000-0000-************
    keyvaultName: "nc-kv-pc-vwfs-int"
    objects:  |
      array:
        - |
          objectName: storage-accout-access-name
          objectType: secret
        - |
          objectName: storage-accout-access-key
          objectType: secret
        - |
          objectName: tas-int-ingress2
          objectType: secret
        - |
          objectName: registry-auth
          objectType: secret

  secretObjects:
  - secretName: kvsecret
    data:
    - key: azurestorageaccountname
      objectName: storage-accout-access-name
    - key: azurestorageaccountkey
      objectName: storage-accout-access-key
    type: Opaque
  - secretName: tas-int-ingress-tls
    type: kubernetes.io/tls
    data:
    - objectName: tas-vwfs-int-ingress2
      key: tls.key
    - objectName: tas-vwfs-int-ingress2
      key: tls.crt
  - secretName: registry-auth
    type: kubernetes.io/dockerconfigjson
    data:
    - key: .dockerconfigjson
      objectName: registry-auth
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: k8s-secret-loader
  namespace: tas-vwfs-int
spec:
  replicas: 1
  selector:
    matchLabels:
      app: k8s-secret-loader
  template:
    metadata:
      labels:
        app: k8s-secret-loader
    spec:
      containers:
      - name: k8s-secret-loader
        image: ubuntu
        command:
          - "sleep"
          - "infinity"
        resources:
          requests:
            memory: "212Mi"
            cpu: "150m"
          limits:
            memory: "312Mi"
            cpu: "200m"
        volumeMounts:
        - name: k8s-secret-loader
          mountPath: "/mnt/secrets-store"
          readOnly: true
      volumes:
        - name: k8s-secret-loader
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: k8s-secret-loader
