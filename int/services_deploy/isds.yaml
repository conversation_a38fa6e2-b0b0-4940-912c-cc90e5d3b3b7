apiVersion: apps/v1
kind: Deployment
metadata:
  name: isds
spec:
  replicas: 1
  selector:
    matchLabels:
      app: isds
  template:
    metadata:
      labels:
        app: isds
    spec:
      containers:
      - name: isds
        image: isdsimage:version
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1500m"
        volumeMounts:
        - name: isds-config
          mountPath: "/app/DatovkaREST/config/production.json"
          readOnly: true
          subPath: "isds.json"
      volumes:
      - name: isds-config
        configMap:
          name: isds-config
---
apiVersion: v1
kind: Service
metadata:
  name: isds
spec:
  selector:
    app: isds
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080 