apiVersion: apps/v1
kind: Deployment
metadata:
  name: elastic
spec:
  replicas: 1
  selector:
    matchLabels:
      app: elastic
  template:
    metadata:
      labels:
        app: elastic
    spec:
      initContainers:
        - name: init-permissions
          image: elasticsearch:7.17.16
          command: [ "sh", "-c", "chown -R 1000:0 /usr/share/elasticsearch/data" ]
          volumeMounts:
            - name: elasticsearch-data
              mountPath: /usr/share/elasticsearch/data
      containers:
      - name: elasticsearch
        image: elasticsearch:7.17.16
        ports:
        - containerPort: 9200
        - containerPort: 9300
        volumeMounts:
        - name: elasticsearch-data
          mountPath: /usr/share/elasticsearch/data
        - name: elastic-secret-loader
          mountPath: "/mnt/secrets-store"
          readOnly: true
        env:
        - name: "discovery.type"
          value: "single-node"
        - name: "ES_JAVA_OPTS"
          value: "-Xms2g -Xmx3g"
        - name: "xpack.security.enabled"
          value: "true"
        - name: ELASTIC_PASSWORD_FILE
          value: "/mnt/secrets-store/elasticpass"
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
      volumes:
      - name: elasticsearch-data
        persistentVolumeClaim:
          claimName: elasticsearch-data-pvc
      - name: elastic-secret-loader
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: elastic-secret-loader

---
apiVersion: v1
kind: Service
metadata:
  name: elastic
spec:
  selector:
    app: elastic
  ports:
    - protocol: TCP
      port: 9200
      targetPort: 9200
