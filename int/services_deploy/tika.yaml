apiVersion: apps/v1
kind: Deployment
metadata:
  name: tika
spec:
  selector:
    matchLabels:
      app: tika
  template:
    metadata:
      labels:
        app: tika
    spec:
      containers:
      - name: tika
        image: apache/tika:2.9.0.0-full
        resources:
          requests:
            memory: "1Gi"
            cpu: "750m"
          limits:
            memory: "2Gi"
            cpu: "1500m"
        ports:
        - containerPort: 9998
---
apiVersion: v1
kind: Service
metadata:
  name: tika
spec:
  selector:
    app: tika
  ports:
  - port: 9998
    targetPort: 9998
