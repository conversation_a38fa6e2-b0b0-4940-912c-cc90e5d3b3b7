kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: elasticsearch-data-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 40Gi
  volumeName: elasticsearch-data-pv
  storageClassName: elastic-premium-retain
---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: isds-storage-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: default
