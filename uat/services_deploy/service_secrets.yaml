apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: elastic-secret-loader
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: 00000000-0000-0000-0000-000000000000
    tenantId: 00000000-0000-0000-0000-000000000000
    keyvaultName: "nc-kv-pc-vwfs-uat"
    filePermission: "0600"
    objects:  |
      array:
        - |
          filePermission: 0600
          objectName: elasticpass
          objectType: secret
---
