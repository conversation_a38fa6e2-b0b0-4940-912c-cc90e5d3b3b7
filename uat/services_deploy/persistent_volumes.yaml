apiVersion: v1
kind: PersistentVolume
metadata:
  name: elasticsearch-data-pv
spec:
  capacity:
    storage: 40Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: elastic-premium-retain
  csi:
    driver: disk.csi.azure.com
    readOnly: false
    volumeHandle: /subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/nc-rg-pc-vwfs-uat/providers/Microsoft.Compute/disks/elasticsearch-data
    volumeAttributes:
      fsType: ext4
