apiVersion: apps/v1
kind: Deployment
metadata:
  name: tas-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tas-backend
  template:
    metadata:
      labels:
        app: tas-backend
      annotations:
        app.kubernetes.io/part-of: "tas"
        app.kubernetes.io/name: "tas-backend"
        app.kubernetes.io/version: "5.7.11-7c1b9b0e86-**********"
    spec:
      imagePullSecrets:
        - name: registry-auth
      containers:
        - name: tas-backend
          image: registry.neit.cz/tas/backend:5.7.11-7c1b9b0e86-**********
          imagePullPolicy: Always
          env:
            - name: TAS_IMAGE_TAG
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['app.kubernetes.io/version']
            - name: NODE_OPTIONS
              value: "--openssl-legacy-provider"
            - name: LANG
              value: "C.UTF-8"
            # TAS Backend Configuration
            - name: PORT
              value: "8001"
            - name: TAS_BACKEND_HOSTNAME
              value: "https://vwfs.uat.teamassistant.app/api"
            - name: TAS_FRONTEND_URL
              value: "https://vwfs.uat.teamassistant.app"
            # Database Configuration
            - name: TAS_DB_HOSTNAME
              value: "vwfs-uat.database.windows.net"
            - name: TAS_DB_PORT
              value: "1433"
            - name: TAS_DB_NAME
              value: "vwfs-uat"
            - name: TAS_DB_REQUEST_TIMEOUT
              value: "120000"
            - name: TAS_DB_ENCRYPTION
              value: "true"
            - name: TAS_DB_CONNECT_TIMEOUT
              value: "30000"
            - name: TAS_DB_POOL_IDLE_TIMEOUT
              value: "120000"
            - name: TAS_DB_CLIENT
              value: "mssql"
            - name: TAS_DB_DEBUG
              value: "false"
            # Active Directory Configuration
            - name: TAS_AD_BINARY_OBJECT_GUID
              value: "true"
            # Mail Configuration
            - name: TAS_MAIL_SENDING_ENABLED
              value: "false"
            # Languages Configuration
            - name: TAS_LANGUAGES
              value: "cs en sk ro pl"
            - name: TAS_DEFAULT_LANGUAGE
              value: "cs"
            # DMS Configuration
            - name: TAS_DMS_TIKA_URL
              value: "http://tika:9998"
            - name: TAS_DMS_ELASTIC_VERSION
              value: "7"
            - name: TAS_DMS_ELASTIC_URL
              value: "http://elastic:9200"
            - name: TAS_DMS_FULLTEXT_INDEX
              value: "tas"
            - name: TAS_DMS_STORAGE_PATH
              value: "/app/tas/dms"
            - name: TAS_DMS_ALLOWED_EXTERNAL_SOURCES
              value: "/app/tas/dms"
            - name: TAS_DMS_ENCRYPT_CONTENT
              value: "false"
            - name: TAS_DMS_ENCRYPT_FILENAMES
              value: "false"
            - name: TAS_DMS_ELASTICSEARCH_SECURED
              value: "true"
            - name: TAS_DMS_ELASTICSEARCH_USERNAME
              value: "elastic"
            # Cron Configuration
            - name: TAS_CRONS_RUN_ON_START
              value: "true"
            # Logger Configuration
            - name: TAS_LOGGER_STDOUT_ENABLE
              value: "false"
            - name: TAS_LOGGER_ELASTIC_ENABLE
              value: "true"
            - name: TAS_LOGGER_ELASTIC_INDEX_NAME
              value: "tas-vwfs-uat-logs"
            - name: TAS_LOGGER_ELASTIC_CONSISTENCY
              value: "one"
            - name: TAS_LOGGER_ELASTIC_URL
              value: "http://elastic:9200"
            - name: TAS_LOGGER_ELASTIC_VERSION
              value: "7"
            - name: TAS_LOGGER_ELASTIC_OP_TYPE
              value: "create"
            - name: TAS_LOGGER_ELASTIC_USERNAME
              value: "elastic"
            - name: TAS_LOGGER_ARANGO_ENABLE
              value: "false"
            # Redis Configuration
            - name: TAS_REDIS_HOST
              value: "vwfs-uat.redis.cache.windows.net"
            - name: TAS_REDIS_PORT
              value: "6380"
            - name: TAS_REDIS_PASSWORD
              value: ""
            - name: TAS_REDIS_TLS
              value: "true"
            # Storage Paths
            - name: TAS_CUSTOM_ASSETS_PATH
              value: "/app/tas/storage/assets"
            - name: TAS_CSV_STORAGE_DIR
              value: "/app/tas/storage/assets/csv"
            - name: TAS_PLUGINS_DESTINATION
              value: "/app/tas/storage/plugins"
            - name: TAS_PATHS_TMP
              value: "/app/tas/storage/tmp"
            - name: TAS_PATHS_USER_PHOTO_PATH
              value: "/app/tas/storage/photos"
            - name: TAS_CACHE_PATH
              value: "/app/tas/storage/cache"
            # Security Configuration
            - name: TAS_SECURITY_SALT_ROUNDS
              value: "4"
          readinessProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 80
            periodSeconds: 2
          livenessProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 80
            periodSeconds: 2
          startupProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 300
            periodSeconds: 5
          resources:
            requests:
              memory: "3Gi"
              cpu: "1000m"
            limits:
              memory: "6Gi"
              cpu: "1500m"
          ports:
            - containerPort: 8001
          volumeMounts:
            - name: tas-secret-loader
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: config
              mountPath: "/app/tas/backend/config/config/local.js"
              readOnly: false
              subPath: "local.js"
            - name: dms-storage
              mountPath: "/app/tas/dms"
              readOnly: false
            - name: backend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: frontend-storage
              mountPath: "/data/tas-front-uploads"
              readOnly: false
      volumes:
        - name: tas-secret-loader
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: tas-secret-loader
        - name: config
          configMap:
            name: tas-backend-config
        - name: dms-storage
          azureFile:
            secretName: kvsecret
            shareName: dms-storage
            readOnly: false
        - name: backend-storage
          azureFile:
            secretName: kvsecret
            shareName: backend-storage
            readOnly: false
        - name: frontend-storage
          azureFile:
            secretName: kvsecret
            shareName: frontend-storage
            readOnly: false
---
apiVersion: v1
kind: Service
metadata:
  name: tas-backend
spec:
  selector:
    app: tas-backend
  ports:
    - port: 80
      targetPort: 8001
