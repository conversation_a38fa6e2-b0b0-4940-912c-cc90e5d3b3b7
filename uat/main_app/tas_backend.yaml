apiVersion: apps/v1
kind: Deployment
metadata:
  name: tas-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tas-backend
  template:
    metadata:
      labels:
        app: tas-backend
      annotations:
        app.kubernetes.io/part-of: "tas"
        app.kubernetes.io/name: "tas-backend"
        app.kubernetes.io/version: "5.7.11-7c1b9b0e86-**********"
    spec:
      imagePullSecrets:
        - name: registry-auth
      containers:
        - name: tas-backend
          image: registry.neit.cz/tas/backend:5.7.11-7c1b9b0e86-**********
          imagePullPolicy: Always
          env:
            - name: TAS_IMAGE_TAG
              valueFrom:
                fieldRef:
                  fieldPath: metadata.annotations['app.kubernetes.io/version']
            - name: NODE_OPTIONS
              value: "--openssl-legacy-provider"
            - name: LANG
              value: "C.UTF-8"
          readinessProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 80
            periodSeconds: 2
          livenessProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 80
            periodSeconds: 2
          startupProbe:
            httpGet:
              path: /status
              port: 8001
            failureThreshold: 300
            periodSeconds: 5
          resources:
            requests:
              memory: "3Gi"
              cpu: "1000m"
            limits:
              memory: "6Gi"
              cpu: "1500m"
          ports:
            - containerPort: 8001
          volumeMounts:
            - name: tas-secret-loader
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: config
              mountPath: "/app/tas/backend/config/config/local.js"
              readOnly: false
              subPath: "local.js"
            - name: dms-storage
              mountPath: "/app/tas/dms"
              readOnly: false
            - name: backend-storage
              mountPath: "/app/tas/storage"
              readOnly: false
            - name: frontend-storage
              mountPath: "/data/tas-front-uploads"
              readOnly: false
      volumes:
        - name: tas-secret-loader
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: tas-secret-loader
        - name: config
          configMap:
            name: tas-backend-config
        - name: dms-storage
          azureFile:
            secretName: kvsecret
            shareName: dms-storage
            readOnly: false
        - name: backend-storage
          azureFile:
            secretName: kvsecret
            shareName: backend-storage
            readOnly: false
        - name: frontend-storage
          azureFile:
            secretName: kvsecret
            shareName: frontend-storage
            readOnly: false
---
apiVersion: v1
kind: Service
metadata:
  name: tas-backend
spec:
  selector:
    app: tas-backend
  ports:
    - port: 80
      targetPort: 8001
