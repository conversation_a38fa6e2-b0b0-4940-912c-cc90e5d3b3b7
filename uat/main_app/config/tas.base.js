const path = require("path");
const fs = require("fs");
var config = {};

const readSecretFile = (path) => {
    try {
        return fs.readFileSync(path, "utf-8").trim();
    } catch {
        return undefined;
    }
};

const getEnvSecret = (envVar, defaultValue = undefined) => {
    const fromEnv = process.env[envVar];
    const filePath = process.env[`${envVar}_FILE`];

    let fromFile = null;
    if (filePath) {
        try {
            fromFile = readSecretFile(filePath);
        } catch (_err) {
            //
        }
    }

    return fromFile || fromEnv || defaultValue;
};

const getEnvBool = (envVar, defaultValue = true) => {
    const envValue = getEnvSecret(envVar);

    if (typeof envValue === 'undefined' || envValue === '') {
        return defaultValue;
    }

    return Number(envValue) === 1 || envValue.toUpperCase().startsWith('Y');
}

const getEnvNumber = (envVar, defaultValue) => {
    const envValue = getEnvSecret(envVar);

    if (typeof envValue === 'undefined' || envValue === '') {
        return defaultValue;
    }

    return Number(envValue);
}

const getEnvJson = (
    envVar,
    defaultValue,
) => {
    const envValue = getEnvSecret(envVar);

    if (typeof envValue === "undefined" || envValue === "") {
        return defaultValue;
    }

    return JSON.parse(envValue);
};


config.backendUrl = getEnvSecret('TAS_BACKEND_URL', 'http://localhost:8001');
config.multitenantBackend = false; // if true then use /api on same domain as backend API endpoint

config.port = getEnvNumber('TAS_FRONTEND_PORT', getEnvNumber('PORT', 8000));
config.webpackPort = 8888;
config.menuLink = null; // {url: 'http://example.cz', title: 'Zápisy', iframe: false, icon: 'icon-wrench', newWindow: false} or [{}, {}] // icon is optional
config.logoUrl = getEnvSecret('TAS_FRONTEND_LOGO_URL', null); // '/assets/images/newLogo.png' - logo must be in assets
config.faviconUrl = null; // use absolute path
config.langs = getEnvSecret('TAS_FRONTEND_LANGUAGES', 'cs sk en ru ro hr pl de sr fr it')
    .split(' ')
    .filter(function (l) {
        return l.length;
    }); // languages available in settings, first language is used to translate login form (for new users)
config.floatingPointPrecision = 6;
config.restLimit = 100000; // default limit applied to request to the backend (by default all request ale limited to 100)
config.clearAuthCache = true; // defaultly enabled
config.dateTimeFormat = null; // null means format according to actual localization, else 'cs-CZ'/'en-US' etc.
config.numberFormat = null; // null means format according to actual localization, else 'cs-CZ'/'en-US' etc.
config.socketNotifs = getEnvBool('TAS_SOCKET_NOTIFS', false);
config.loginPage = getEnvSecret('TAS_FRONTEND_LOGIN_PAGE', '/login'); // defaultly '/login'
config.authorizationHeader = getEnvSecret('TAS_FRONTEND_AUTHORIZATION_HEADER', 'Authorization'); // Request authorization header - defaultly Authorization
config.unauthorizedStatusCode = getEnvNumber('TAS_UNAUTHORIZED_STATUS_CODE', 401);
config.responseHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'X-Requested-With',
    'X-Frame-Options': 'sameorigin', // clickjacking prevention
    // 'X-Content-Type-Options': 'nosniff',
    // 'Referrer-Policy': 'origin',
    // 'Access-Control-Expose-Headers': '',
    // 'Cross-Origin-Opener-Policy': 'same-origin',
    // 'Access-Control-Max-Age': 60,
};
config.assetsStoragePath = getEnvSecret('TAS_FRONTEND_ASSETS_PATH', '/app/tas/storage/assets');
config.localhostUrl = 'http://localhost';
config.webpackDevServerOptions = {}; // can be extended or overwritten
config.contentfullAccessToken = getEnvSecret('TAS_FRONTEND_CONTENTFUL_TOKEN', '');
config.maxUploadSize = 200; // Upload limit for bodyParser. In MB
config.hotjarId = getEnvSecret('TAS_FRONTEND_HJ_ID', null);
config.hotjarSv = getEnvSecret('TAS_FRONTEND_HJ_SV', null);

module.exports = config;
