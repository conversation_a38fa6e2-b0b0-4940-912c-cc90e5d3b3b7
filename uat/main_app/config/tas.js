/**
 * TAS Frontend Configuration
 * This configuration uses environment variables with fallback to default values
 */

// Helper function to get environment variable with default value
const getEnv = (key, defaultValue = undefined) => {
    return process.env[key] || defaultValue;
};

// Helper function to get boolean environment variable
const getEnvBool = (key, defaultValue = false) => {
    const value = process.env[key];
    if (value === undefined || value === '') {
        return defaultValue;
    }
    return value === 'true' || value === '1' || value.toLowerCase() === 'yes';
};

// Helper function to get number environment variable
const getEnvNumber = (key, defaultValue = undefined) => {
    const value = process.env[key];
    if (value === undefined || value === '') {
        return defaultValue;
    }
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
};

// Helper function to parse JSON environment variable
const getEnvJson = (key, defaultValue = undefined) => {
    const value = process.env[key];
    if (value === undefined || value === '') {
        return defaultValue;
    }
    try {
        return JSON.parse(value);
    } catch (e) {
        console.warn(`Failed to parse JSON for ${key}:`, e.message);
        return defaultValue;
    }
};

// Configuration object
const config = {
    backendUrl: getEnv('TAS_BACKEND_URL', 'https://vwfs.uat.teamassistant.app/api'),
    multitenantBackend: getEnvBool('TAS_MULTITENANT_BACKEND', false),
    port: getEnvNumber('TAS_FRONTEND_PORT', 8000),
    menuLink: getEnvJson('TAS_MENU_LINKS_JSON', [
        { url: '/assets/uploads/pdf', title: 'Manuals', newWindow: false, iframe: true }
    ]),
    cloneDynRows: getEnvBool('TAS_CLONE_DYN_ROWS', true),
    socketNotifs: getEnvBool('TAS_SOCKET_NOTIFS', false),
    responseHeaders: {
        'X-Frame-Options': getEnv('TAS_RESPONSE_HEADERS_X_FRAME_OPTIONS', 'sameorigin'),
        'X-Content-Type-Options': getEnv('TAS_RESPONSE_HEADERS_X_CONTENT_TYPE_OPTIONS', 'nosniff'),
        'X-Permitted-Cross-Domain-Policies': getEnv('TAS_RESPONSE_HEADERS_X_PERMITTED_CROSS_DOMAIN_POLICIES', 'none'),
        'Referrer-Policy': getEnv('TAS_RESPONSE_HEADERS_REFERRER_POLICY', 'origin'),
        'Cross-Origin-Resource-Policy': getEnv('TAS_RESPONSE_HEADERS_CROSS_ORIGIN_RESOURCE_POLICY', 'cross-origin'),
        'Cross-Origin-Opener-Policy': getEnv('TAS_RESPONSE_HEADERS_CROSS_ORIGIN_OPENER_POLICY', 'same-origin'),
        'Content-Security-Policy': getEnv('TAS_RESPONSE_HEADERS_CSP', `default-src 'self' 'unsafe-inline' 'unsafe-eval' data: vwfs-back.uat.teamassistant.app`),
    },
    assetsStoragePath: getEnv('TAS_ASSETS_STORAGE_PATH', '/app/tas/storage/assets')
};

module.exports = config;
