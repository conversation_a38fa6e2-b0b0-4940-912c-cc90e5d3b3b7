# TAS Frontend Configuration Environment Variables
# Copy this file to .env.frontend and modify values as needed

# Server Configuration
TAS_FRONTEND_PORT=8000
TAS_BACKEND_URL=https://vwfs.uat.teamassistant.app/api
TAS_MULTITENANT_BACKEND=false

# Feature Flags
TAS_CLONE_DYN_ROWS=true
TAS_SOCKET_NOTIFS=false

# Menu Configuration
TAS_MENU_LINKS_JSON=[{"url":"/assets/uploads/pdf","title":"Manuals","newWindow":false,"iframe":true}]

# Security Headers
TAS_RESPONSE_HEADERS_X_FRAME_OPTIONS=sameorigin
TAS_RESPONSE_HEADERS_X_CONTENT_TYPE_OPTIONS=nosniff
TAS_RESPONSE_HEADERS_X_PERMITTED_CROSS_DOMAIN_POLICIES=none
TAS_RESPONSE_HEADERS_REFERRER_POLICY=origin
TAS_RESPONSE_HEADERS_CROSS_ORIGIN_RESOURCE_POLICY=cross-origin
TAS_RESPONSE_HEADERS_CROSS_ORIGIN_OPENER_POLICY=same-origin
TAS_RESPONSE_HEADERS_CSP=default-src 'self' 'unsafe-inline' 'unsafe-eval' data: vwfs-back.uat.teamassistant.app

# Storage Configuration
TAS_ASSETS_STORAGE_PATH=/app/tas/storage/assets
