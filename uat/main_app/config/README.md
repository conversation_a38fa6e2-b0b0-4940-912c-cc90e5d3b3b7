# TAS Configuration Migration to Environment Variables

This document describes the migration of TAS configuration from hardcoded JavaScript files to environment variables.

## Overview

The TAS application configuration has been migrated from hardcoded values in `local.js` and `tas.js` to environment variables. This provides better flexibility for different deployment environments and follows the [12-factor app methodology](https://12factor.net/config).

## Files Changed

### Frontend Configuration
- **File**: `uat/main_app/config/tas.js`
- **Changes**: Modified to read configuration from environment variables instead of hardcoded values
- **Deployment**: `uat/main_app/tas_front.yaml`

### Backend Configuration
- **File**: `uat/main_app/config/local.js` (unchanged - still reads from files and environment)
- **Deployment**: `uat/main_app/tas_backend.yaml` and `uat/main_app/tas_backend_cron.yaml`

## Environment Variables

### Frontend Variables

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `TAS_FRONTEND_PORT` | `8000` | Frontend server port |
| `TAS_BACKEND_URL` | `https://vwfs.uat.teamassistant.app/api` | Backend API URL |
| `TAS_MULTITENANT_BACKEND` | `false` | Enable multitenant backend |
| `TAS_CLONE_DYN_ROWS` | `true` | Enable dynamic row cloning |
| `TAS_SOCKET_NOTIFS` | `false` | Enable socket notifications |
| `TAS_MENU_LINKS_JSON` | `[{"url":"/assets/uploads/pdf","title":"Manuals","newWindow":false,"iframe":true}]` | Menu links configuration (JSON) |
| `TAS_RESPONSE_HEADERS_X_FRAME_OPTIONS` | `sameorigin` | X-Frame-Options header |
| `TAS_RESPONSE_HEADERS_X_CONTENT_TYPE_OPTIONS` | `nosniff` | X-Content-Type-Options header |
| `TAS_RESPONSE_HEADERS_X_PERMITTED_CROSS_DOMAIN_POLICIES` | `none` | X-Permitted-Cross-Domain-Policies header |
| `TAS_RESPONSE_HEADERS_REFERRER_POLICY` | `origin` | Referrer-Policy header |
| `TAS_RESPONSE_HEADERS_CROSS_ORIGIN_RESOURCE_POLICY` | `cross-origin` | Cross-Origin-Resource-Policy header |
| `TAS_RESPONSE_HEADERS_CROSS_ORIGIN_OPENER_POLICY` | `same-origin` | Cross-Origin-Opener-Policy header |
| `TAS_RESPONSE_HEADERS_CSP` | `default-src 'self' 'unsafe-inline' 'unsafe-eval' data: vwfs-back.uat.teamassistant.app` | Content Security Policy |
| `TAS_ASSETS_STORAGE_PATH` | `/app/tas/storage/assets` | Assets storage path |

### Backend Variables

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `PORT` | `8001` | Backend server port |
| `TAS_BACKEND_HOSTNAME` | `https://vwfs.uat.teamassistant.app/api` | Backend hostname |
| `TAS_FRONTEND_URL` | `https://vwfs.uat.teamassistant.app` | Frontend URL |
| `TAS_DB_HOSTNAME` | `vwfs-uat.database.windows.net` | Database hostname |
| `TAS_DB_PORT` | `1433` | Database port |
| `TAS_DB_NAME` | `vwfs-uat` | Database name |
| `TAS_DB_REQUEST_TIMEOUT` | `120000` | Database request timeout (ms) |
| `TAS_DB_ENCRYPTION` | `true` | Enable database encryption |
| `TAS_DB_CONNECT_TIMEOUT` | `30000` | Database connection timeout (ms) |
| `TAS_DB_POOL_IDLE_TIMEOUT` | `120000` | Database pool idle timeout (ms) |
| `TAS_DB_CLIENT` | `mssql` | Database client type |
| `TAS_DB_DEBUG` | `false` | Enable database debug logging |
| `TAS_AD_BINARY_OBJECT_GUID` | `true` | Active Directory binary object GUID |
| `TAS_MAIL_SENDING_ENABLED` | `false` | Enable mail sending |
| `TAS_LANGUAGES` | `cs en sk ro pl` | Supported languages |
| `TAS_DEFAULT_LANGUAGE` | `cs` | Default language |

## Secret Management

The following secrets are still read from mounted files in Kubernetes:
- Database username: `/mnt/secrets-store/sqluserusr`
- Database password: `/mnt/secrets-store/sqluserpw`
- Elasticsearch password: `/mnt/secrets-store/elasticpass`

## Deployment

The environment variables are now configured directly in the Kubernetes deployment files:
- `tas_front.yaml` - Frontend environment variables
- `tas_backend.yaml` - Backend environment variables
- `tas_backend_cron.yaml` - Backend cron environment variables

## Migration Process

1. **Backup**: Original configuration files are preserved
2. **Environment Variables**: Added to Kubernetes deployment YAML files
3. **Code Changes**: Updated `tas.js` to read from environment variables
4. **Testing**: Verify configuration is loaded correctly

## Usage

### Local Development

1. Copy `.env.example` to `.env`
2. Modify values as needed for your local environment
3. Load environment variables before starting the application

### Production Deployment

Environment variables are configured in the Kubernetes deployment files and will be automatically available to the containers.

## Troubleshooting

### Configuration Not Loading
- Check that environment variables are properly set in the deployment YAML
- Verify the container has access to the environment variables
- Check application logs for configuration parsing errors

### JSON Configuration Errors
- Ensure JSON environment variables (like `TAS_MENU_LINKS_JSON`) are properly escaped
- Validate JSON syntax before deployment

## Next Steps

Consider moving additional configuration to environment variables:
- Database connection details (currently in `local.js`)
- Additional service URLs and endpoints
- Feature flags and toggles
