# TAS Application Configuration Environment Variables
# Copy this file to .env and modify values as needed

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Server Configuration
TAS_FRONTEND_PORT=8000
TAS_BACKEND_URL=https://vwfs.uat.teamassistant.app/api
TAS_MULTITENANT_BACKEND=false

# Feature Flags
TAS_CLONE_DYN_ROWS=true
TAS_SOCKET_NOTIFS=false

# Menu Configuration (JSON format)
TAS_MENU_LINKS_JSON=[{"url":"/assets/uploads/pdf","title":"Manuals","newWindow":false,"iframe":true}]

# Security Headers
TAS_RESPONSE_HEADERS_X_FRAME_OPTIONS=sameorigin
TAS_RESPONSE_HEADERS_X_CONTENT_TYPE_OPTIONS=nosniff
TAS_RESPONSE_HEADERS_X_PERMITTED_CROSS_DOMAIN_POLICIES=none
TAS_RESPONSE_HEADERS_REFERRER_POLICY=origin
TAS_RESPONSE_HEADERS_CROSS_ORIGIN_RESOURCE_POLICY=cross-origin
TAS_RESPONSE_HEADERS_CROSS_ORIGIN_OPENER_POLICY=same-origin
TAS_RESPONSE_HEADERS_CSP=default-src 'self' 'unsafe-inline' 'unsafe-eval' data: vwfs-back.uat.teamassistant.app

# Storage Configuration
TAS_ASSETS_STORAGE_PATH=/app/tas/storage/assets

# =============================================================================
# BACKEND CONFIGURATION (from local.js)
# =============================================================================

# Server Configuration
PORT=8001
TAS_BACKEND_HOSTNAME=https://vwfs.uat.teamassistant.app/api
TAS_FRONTEND_URL=https://vwfs.uat.teamassistant.app

# Database Configuration
TAS_DB_HOSTNAME=vwfs-uat.database.windows.net
TAS_DB_PORT=1433
TAS_DB_USERNAME=TAS
TAS_DB_PASSWORD=
TAS_DB_NAME=vwfs-uat
TAS_DB_REQUEST_TIMEOUT=120000
TAS_DB_ENCRYPTION=true
TAS_DB_CONNECT_TIMEOUT=30000
TAS_DB_POOL_IDLE_TIMEOUT=120000
TAS_DB_CLIENT=mssql
TAS_DB_DEBUG=false

# Active Directory Configuration
TAS_AD_BINARY_OBJECT_GUID=true

# Mail Configuration
TAS_MAIL_SENDING_ENABLED=false

# Languages Configuration
TAS_LANGUAGES=cs en sk ro pl
TAS_DEFAULT_LANGUAGE=cs

# DMS (Document Management System) Configuration
TAS_DMS_TIKA_URL=http://tika:9998
TAS_DMS_ELASTIC_VERSION=7
TAS_DMS_ELASTIC_URL=http://elastic:9200
TAS_DMS_FULLTEXT_INDEX=tas
TAS_DMS_STORAGE_PATH=/app/tas/dms
TAS_DMS_ALLOWED_EXTERNAL_SOURCES=/app/tas/dms
TAS_DMS_ENCRYPT_CONTENT=false
TAS_DMS_ENCRYPT_FILENAMES=false
TAS_DMS_ELASTICSEARCH_SECURED=true
TAS_DMS_ELASTICSEARCH_USERNAME=elastic
TAS_DMS_ELASTICSEARCH_PASSWORD=

# Cron Configuration
TAS_CRONS_RUN_ON_START=true

# Logger Configuration
TAS_LOGGER_STDOUT_ENABLE=false
TAS_LOGGER_ELASTIC_ENABLE=true
TAS_LOGGER_ELASTIC_INDEX_NAME=tas-vwfs-uat-logs
TAS_LOGGER_ELASTIC_CONSISTENCY=one
TAS_LOGGER_ELASTIC_URL=http://elastic:9200
TAS_LOGGER_ELASTIC_VERSION=7
TAS_LOGGER_ELASTIC_OP_TYPE=create
TAS_LOGGER_ELASTIC_USERNAME=elastic
TAS_LOGGER_ELASTIC_PASSWORD=
TAS_LOGGER_ARANGO_ENABLE=false

# Redis Configuration
TAS_REDIS_HOST=vwfs-uat.redis.cache.windows.net
TAS_REDIS_PORT=6380
TAS_REDIS_PASSWORD=
TAS_REDIS_TLS=true

# Storage Paths
TAS_CUSTOM_ASSETS_PATH=/app/tas/storage/assets
TAS_CSV_STORAGE_DIR=/app/tas/storage/assets/csv
TAS_PLUGINS_DESTINATION=/app/tas/storage/plugins
TAS_PATHS_TMP=/app/tas/storage/tmp
TAS_PATHS_USER_PHOTO_PATH=/app/tas/storage/photos
TAS_CACHE_PATH=/app/tas/storage/cache

# Security Configuration
TAS_SECURITY_SALT_ROUNDS=4

# Secret Files (for Kubernetes secret mounting)
TAS_DB_USERNAME_FILE=/mnt/secrets-store/sqluserusr
TAS_DB_PASSWORD_FILE=/mnt/secrets-store/sqluserpw
TAS_ELASTICSEARCH_PASSWORD_FILE=/mnt/secrets-store/elasticpass
